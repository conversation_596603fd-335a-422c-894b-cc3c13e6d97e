package com.shoalter.mms_product_api.service.product;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterSearchVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterUpdateVisibilityResponseDto;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * 這個測試類用於重現 BatchEditInvisibleFlagService 中線程池耗盡的問題
 * 
 * 問題分析：
 * 1. 原始線程池配置沒有設置隊列容量，使用默認的 Integer.MAX_VALUE
 * 2. 沒有設置拒絕策略，默認使用 AbortPolicy
 * 3. 使用 CompletableFuture.allOf().join() 可能導致死鎖
 * 4. 嵌套的 @Async 調用可能導致線程池耗盡
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class ThreadPoolExhaustionReproductionTest {

    @Mock
    private ProductMasterHelper productMasterHelper;

    @Mock
    private SaveProductHelper saveProductHelper;

    private Gson gson = new Gson();

    private BatchEditInvisibleFlagService batchEditInvisibleFlagService;
    private ThreadPoolTaskExecutor problematicExecutor;
    private AtomicInteger taskCounter = new AtomicInteger(0);

    @BeforeEach
    void setUp() {
        // 創建有問題的線程池配置（模擬原始配置）
        problematicExecutor = createProblematicThreadPool();
        
        batchEditInvisibleFlagService = new BatchEditInvisibleFlagService(
                problematicExecutor, saveProductHelper, productMasterHelper, gson);
        
        // 設置分區大小
        ReflectionTestUtils.setField(batchEditInvisibleFlagService, "updateInvisiblePartitionSize", 2);
    }

    /**
     * 創建有問題的線程池配置，模擬原始問題
     */
    private ThreadPoolTaskExecutor createProblematicThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);  // 很小的核心線程數
        executor.setMaxPoolSize(4);   // 很小的最大線程數
        // 沒有設置隊列容量，默認使用 Integer.MAX_VALUE（無界隊列）
        // 沒有設置拒絕策略，默認使用 AbortPolicy
        executor.setThreadNamePrefix("problematic-");
        executor.initialize();
        return executor;
    }

    @Test
    void testThreadPoolExhaustionWithSlowTasks() throws InterruptedException {
        log.info("=== 測試線程池耗盡問題 ===");
        
        // 準備大量測試數據
        List<EditInvisibleProductRequestDto> products = createLargeProductList(20);
        EditInvisibleRequestDto request = new EditInvisibleRequestDto();
        request.setProducts(products);
        request.setInvisible(true);

        // Mock 返回大量數據
        List<ProductMasterSearchVisibilityResponseDto> responseData = createLargeResponseData(100);
        ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> productMasterResponse = 
                new ProductMasterBaseResponseDto<>();
        productMasterResponse.setData(responseData);

        when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(anyList()))
                .thenReturn(productMasterResponse);

        // Mock saveProductHelper 返回慢速操作
        when(saveProductHelper.updateInvisible(anyString(), anyBoolean()))
                .thenAnswer(invocation -> {
                    int taskId = taskCounter.incrementAndGet();
                    log.info("Task {} started on thread: {}", taskId, Thread.currentThread().getName());
                    
                    // 模擬慢速操作
                    try {
                        Thread.sleep(2000); // 2秒延遲
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("Task {} interrupted", taskId);
                        return "Task interrupted";
                    }
                    
                    log.info("Task {} completed on thread: {}", taskId, Thread.currentThread().getName());
                    return null; // 成功
                });

        // 監控線程池狀態
        CountDownLatch startLatch = new CountDownLatch(1);
        Thread monitorThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 30; i++) {
                    logThreadPoolStatus("Monitor-" + i);
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        monitorThread.start();

        // 使用 MockedStatic 來模擬 SpringBeanProvider
        try (MockedStatic<SpringBeanProvider> mockedSpringBeanProvider = mockStatic(SpringBeanProvider.class)) {
            mockedSpringBeanProvider.when(() -> SpringBeanProvider.getBean(BatchEditInvisibleFlagService.class))
                    .thenReturn(batchEditInvisibleFlagService);

            startLatch.countDown();
            
            long startTime = System.currentTimeMillis();
            
            // 執行測試 - 這裡應該會遇到線程池問題
            assertThrows(Exception.class, () -> {
                batchEditInvisibleFlagService.start(request);
            }, "應該拋出異常，因為線程池配置有問題");
            
            long endTime = System.currentTimeMillis();
            log.info("測試執行時間: {} ms", endTime - startTime);
        }

        monitorThread.interrupt();
    }

    @Test
    void testDeadlockScenario() throws InterruptedException {
        log.info("=== 測試死鎖場景 ===");
        
        // 創建一個更小的線程池來更容易觸發問題
        ThreadPoolTaskExecutor smallExecutor = new ThreadPoolTaskExecutor();
        smallExecutor.setCorePoolSize(1);
        smallExecutor.setMaxPoolSize(1);
        smallExecutor.setThreadNamePrefix("deadlock-");
        smallExecutor.initialize();
        
        BatchEditInvisibleFlagService deadlockService = new BatchEditInvisibleFlagService(
                smallExecutor, saveProductHelper, productMasterHelper, gson);
        ReflectionTestUtils.setField(deadlockService, "updateInvisiblePartitionSize", 1);

        // 準備測試數據
        List<EditInvisibleProductRequestDto> products = createLargeProductList(5);
        EditInvisibleRequestDto request = new EditInvisibleRequestDto();
        request.setProducts(products);
        request.setInvisible(true);

        // Mock 返回數據
        List<ProductMasterSearchVisibilityResponseDto> responseData = createLargeResponseData(10);
        ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> productMasterResponse = 
                new ProductMasterBaseResponseDto<>();
        productMasterResponse.setData(responseData);

        when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(anyList()))
                .thenReturn(productMasterResponse);

        // Mock 長時間運行的任務
        when(saveProductHelper.updateInvisible(anyString(), anyBoolean()))
                .thenAnswer(invocation -> {
                    log.info("Long running task on thread: {}", Thread.currentThread().getName());
                    try {
                        Thread.sleep(5000); // 5秒延遲
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        return "Interrupted";
                    }
                    return null;
                });

        try (MockedStatic<SpringBeanProvider> mockedSpringBeanProvider = mockStatic(SpringBeanProvider.class)) {
            mockedSpringBeanProvider.when(() -> SpringBeanProvider.getBean(BatchEditInvisibleFlagService.class))
                    .thenReturn(deadlockService);

            // 這個測試應該會超時或死鎖
            assertThrows(Exception.class, () -> {
                deadlockService.start(request);
            }, "應該因為死鎖或超時而失敗");
        }
        
        smallExecutor.shutdown();
    }

    @Test
    void testResourceLeakage() {
        log.info("=== 測試資源洩漏 ===");
        
        // 記錄初始線程池狀態
        logThreadPoolStatus("Initial");
        
        // 執行多次操作來觀察資源是否正確釋放
        for (int i = 0; i < 5; i++) {
            try {
                executeSimpleOperation(i);
                logThreadPoolStatus("After operation " + i);
            } catch (Exception e) {
                log.error("Operation {} failed: {}", i, e.getMessage());
            }
        }
        
        // 等待一段時間讓線程池穩定
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        logThreadPoolStatus("Final");
        
        // 驗證線程池狀態
        assertTrue(problematicExecutor.getActiveCount() >= 0, "活躍線程數應該為非負數");
    }

    private void executeSimpleOperation(int operationId) {
        List<EditInvisibleProductRequestDto> products = createLargeProductList(2);
        EditInvisibleRequestDto request = new EditInvisibleRequestDto();
        request.setProducts(products);
        request.setInvisible(true);

        when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(anyList()))
                .thenReturn(null); // 返回 null 來快速結束

        try (MockedStatic<SpringBeanProvider> mockedSpringBeanProvider = mockStatic(SpringBeanProvider.class)) {
            mockedSpringBeanProvider.when(() -> SpringBeanProvider.getBean(BatchEditInvisibleFlagService.class))
                    .thenReturn(batchEditInvisibleFlagService);

            batchEditInvisibleFlagService.start(request);
        }
    }

    private List<EditInvisibleProductRequestDto> createLargeProductList(int count) {
        List<EditInvisibleProductRequestDto> products = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            EditInvisibleProductRequestDto product = new EditInvisibleProductRequestDto();
            product.setStorefrontStoreCode("HKTVMALL");
            product.setProductCodes(Arrays.asList("product" + i));
            products.add(product);
        }
        return products;
    }

    private List<ProductMasterSearchVisibilityResponseDto> createLargeResponseData(int count) {
        List<ProductMasterSearchVisibilityResponseDto> responseData = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            ProductMasterSearchVisibilityResponseDto response = new ProductMasterSearchVisibilityResponseDto();
            response.setUuid("uuid" + i);
            response.setStoreSkuId("sku" + i);
            responseData.add(response);
        }
        return responseData;
    }

    private void logThreadPoolStatus(String phase) {
        ThreadPoolExecutor executor = problematicExecutor.getThreadPoolExecutor();
        log.info("ThreadPool status at {}: corePoolSize={}, maxPoolSize={}, activeCount={}, poolSize={}, queueSize={}, completedTaskCount={}",
                phase,
                executor.getCorePoolSize(),
                executor.getMaximumPoolSize(),
                executor.getActiveCount(),
                executor.getPoolSize(),
                executor.getQueue().size(),
                executor.getCompletedTaskCount());
    }
}
